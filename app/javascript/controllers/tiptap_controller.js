import { Controller } from "@hotwired/stimulus"
import { Editor } from "@tiptap/core"
import StarterKit from "@tiptap/starter-kit"
import Image from "@tiptap/extension-image"
import TextAlign from "@tiptap/extension-text-align"

export default class extends Controller {
  static targets = ["editor", "input", "fileInput"]
  static values = {
    content: String
  }

  connect() {
    this.editor = new Editor({
      element: this.editorTarget,
      extensions: [
        StarterKit,
        Image,
        TextAlign.configure({
          types: ['heading', 'paragraph'],
        }),
      ],
      editorProps: {
        attributes: {
          class: 'prose max-w-none focus:outline-none p-2',
        },
      },
      // Use the JSON-parsed content from the data-attribute
      content: JSON.parse(this.contentValue || '""'),
      onUpdate: ({ editor }) => {
        // On every update, sync the Tiptap content to the hidden input
        this.inputTarget.value = editor.getHTML();
      },
    })

    // When the editor div is clicked, focus the editor
    this.editorTarget.addEventListener('click', (event) => {
      // prevent focusing when a button inside is clicked
      if (event.target === this.editorTarget) {
        this.editor.chain().focus().run()
      }
    })
  }

  disconnect() {
    this.editor.destroy()
  }

  // Image by URL
  insertImageByUrl() {
    const url = prompt("Enter image URL")
    if (url) {
      this.editor.chain().focus().setImage({ src: url }).run()
    }
  }

  // Upload image (local)
  async uploadFile(event) {
    const file = event.target.files[0]
    if (!file) return

    // In production you’d POST file to ActiveStorage, get back a URL.
    // For now, quick preview:
    const reader = new FileReader()
    reader.onload = () => {
      this.editor.chain().focus().setImage({ src: reader.result }).run()
    }
    reader.readAsDataURL(file)
  }

  // Text alignment
  setAlign(event) {
    const align = event.target.dataset.align
    this.editor.chain().focus().setTextAlign(align).run()
  }
}
