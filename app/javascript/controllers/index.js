// This file is responsible for registering all your Stimulus controllers.
// It's compatible with esbuild and does not use `require.context`.

import { application } from "./application"

// Import and register all your controllers here
import TiptapController from "./tiptap_controller.js"
application.register("tiptap", TiptapController)

// Import and register your other controllers
import HelloController from "./hello_controller.js"
application.register("hello", HelloController)
